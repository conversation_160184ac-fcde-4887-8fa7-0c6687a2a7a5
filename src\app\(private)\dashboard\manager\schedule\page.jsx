"use client";

import React, { useState, useEffect, useRef } from 'react';
import * as XLSX from 'xlsx';
import _ from 'lodash';

export default function SchedulePage() {
  const [lichData, setLichData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [uploadStatus, setUploadStatus] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isAdminView, setIsAdminView] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [editFormData, setEditFormData] = useState({});
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newItemData, setNewItemData] = useState({
    ngayLich: '',
    buoiLich: 'Sáng',
    loaiAn: '',
    phongXu: '',
    nguyenDon: '',
    biDon: '',
    viecKien: '',
    hoTen: '',
    noiXu: ''
  });
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState('ngayLich');
  const [sortDirection, setSortDirection] = useState('asc');
  const [backupData, setBackupData] = useState(null);
  const [adminTab, setAdminTab] = useState('upload'); // 'upload', 'add', 'backup'
  
  const fileInputRef = useRef(null);
  const modalRef = useRef(null);
  const addModalRef = useRef(null);
  const backupFileRef = useRef(null);

  // Hàm chuyển đổi định dạng ngày
  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    
    // Nếu dateStr là đối tượng Date, chuyển đổi thành chuỗi
    if (dateStr instanceof Date) {
      return `${String(dateStr.getDate()).padStart(2, '0')}/${String(dateStr.getMonth() + 1).padStart(2, '0')}/${dateStr.getFullYear()}`;
    }
    
    // Nếu là chuỗi, xử lý như trước
    if (typeof dateStr === 'string') {
      try {
        const parts = dateStr.split('-');
        if (parts.length !== 3) return dateStr;
        
        const months = {
          'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
          'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };
        
        const day = parts[0].padStart(2, '0');
        const month = months[parts[1]] || parts[1];
        let year = parts[2];
        if (year.length === 2) {
          year = '20' + year;
        }
        
        return `${day}/${month}/${year}`;
      } catch (e) {
        return dateStr; // Trả về nguyên bản nếu không chuyển đổi được
      }
    }
    
    return String(dateStr); // Trả về dạng chuỗi cho các trường hợp khác
  };

  // Hàm parse ngày từ chuỗi để so sánh
  const parseDate = (dateStr) => {
    if (!dateStr) return null;
    
    try {
      // Nếu là định dạng DD/MM/YYYY
      if (dateStr.includes('/')) {
        const [day, month, year] = dateStr.split('/');
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
      
      // Nếu là định dạng DD-MMM-YY
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts.length !== 3) return null;
        
        const months = {
          'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
          'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
        };
        
        const day = parseInt(parts[0]);
        const month = months[parts[1]];
        let year = parseInt(parts[2]);
        if (year < 100) {
          year = 2000 + year;
        }
        
        if (isNaN(day) || month === undefined || isNaN(year)) return null;
        
        return new Date(year, month, day);
      }
    } catch (e) {
      console.error('Error parsing date:', e);
      return null;
    }
    
    return null;
  };

  // Hàm chuyển đổi chuỗi ngày để sắp xếp
  const getDateValue = (dateStr) => {
    const date = parseDate(dateStr);
    return date ? date.getTime() : 0;
  };

  // Chuẩn hóa dữ liệu từ Excel
  const normalizeExcelData = (data) => {
    return data.map((item, index) => {
      // Đảm bảo kiểu dữ liệu an toàn cho tất cả các trường
      let ngayLich = item['ngay lich'] || '';
      
      // Chuyển đổi ngày thành chuỗi nếu là đối tượng Date
      if (ngayLich instanceof Date) {
        ngayLich = `${String(ngayLich.getDate()).padStart(2, '0')}-${['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'][ngayLich.getMonth()]}-${String(ngayLich.getFullYear()).slice(-2)}`;
      }
      
      return {
        id: item.id || `item-${index}-${Date.now()}`, // Đảm bảo mỗi item có một ID duy nhất
        ngayLich: ngayLich,
        buoiLich: item['buoi lich'] || '',
        loaiAn: item['loai an'] || '',
        phongXu: item['phong xu'] || '',
        nguyenDon: item['nguyen don'] || '',
        biDon: item['bi don/so bc'] || '',
        viecKien: item['viec kien'] || '',
        hoTen: item['ho ten'] || '',
        noiXu: item['noi xu'] || ''
      };
    });
  };

  // Lấy dữ liệu từ API
  const fetchDataFromServer = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/schedule');
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Đảm bảo mỗi item đều có id
      const itemsWithIds = (data.items || []).map((item, index) => {
        if (!item.id) {
          return { ...item, id: `item-${index}-${Date.now()}` };
        }
        return item;
      });
      
      setLichData(itemsWithIds);
      if (data.lastUpdated) {
        setLastUpdated(new Date(data.lastUpdated));
      }
      
      return itemsWithIds;
    } catch (error) {
      console.error('Error fetching data from server:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Lưu dữ liệu vào server
  const saveDataToServer = async (data) => {
    try {
      const response = await fetch('/api/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.lastUpdated) {
        setLastUpdated(new Date(result.lastUpdated));
      }
      
      return true;
    } catch (error) {
      console.error('Error saving data to server:', error);
      return false;
    }
  };

  // Xử lý tải lên file Excel mới
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setUploadStatus({ type: 'loading', message: 'Đang xử lý file...' });
      
      // Đọc file Excel đã tải lên
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { cellDates: true, cellStyles: true });
          
          // Lấy sheet đầu tiên
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // Chuyển đổi dữ liệu
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          // Chuẩn hóa dữ liệu
          const normalizedData = normalizeExcelData(jsonData);
          
          // Lưu vào server
          const saveSuccess = await saveDataToServer(normalizedData);
          
          if (saveSuccess) {
            // Cập nhật state
            setLichData(normalizedData);
            
            setUploadStatus({ type: 'success', message: 'Tải lên thành công!' });
          } else {
            setUploadStatus({ type: 'error', message: 'Lỗi khi lưu dữ liệu. Vui lòng thử lại.' });
          }
          
          // Xóa file sau khi xử lý xong
          setTimeout(() => {
            setUploadStatus(null);
            if (fileInputRef.current) {
              fileInputRef.current.value = '';
            }
          }, 3000);
        } catch (error) {
          console.error('Error processing Excel file:', error);
          setUploadStatus({ type: 'error', message: 'Lỗi xử lý file Excel. Vui lòng kiểm tra định dạng file.' });
        }
      };
      
      reader.onerror = () => {
        setUploadStatus({ type: 'error', message: 'Lỗi đọc file. Vui lòng thử lại.' });
      };
      
      reader.readAsArrayBuffer(file);
    } catch (err) {
      console.error('Error handling file upload:', err);
      setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi tải file. Vui lòng thử lại.' });
    }
  };

  // Xử lý tải lên file backup JSON
  const handleBackupUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setUploadStatus({ type: 'loading', message: 'Đang xử lý file sao lưu...' });
      
      // Đọc file JSON đã tải lên
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const content = e.target.result;
          const data = JSON.parse(content);
          
          if (Array.isArray(data)) {
            // Đảm bảo mỗi item đều có id
            const itemsWithIds = data.map((item, index) => {
              if (!item.id) {
                return { ...item, id: `item-${index}-${Date.now()}` };
              }
              return item;
            });
            
            // Lưu vào server
            const saveSuccess = await saveDataToServer(itemsWithIds);
            
            if (saveSuccess) {
              // Cập nhật state
              setLichData(itemsWithIds);
              
              setUploadStatus({ type: 'success', message: 'Khôi phục sao lưu thành công!' });
            } else {
              setUploadStatus({ type: 'error', message: 'Lỗi khi lưu dữ liệu. Vui lòng thử lại.' });
            }
          } else {
            setUploadStatus({ type: 'error', message: 'File sao lưu không đúng định dạng.' });
          }
          
          // Xóa file sau khi xử lý xong
          setTimeout(() => {
            setUploadStatus(null);
            if (backupFileRef.current) {
              backupFileRef.current.value = '';
            }
          }, 3000);
        } catch (error) {
          console.error('Error processing backup file:', error);
          setUploadStatus({ type: 'error', message: 'Lỗi xử lý file sao lưu. Định dạng file không hợp lệ.' });
        }
      };
      
      reader.onerror = () => {
        setUploadStatus({ type: 'error', message: 'Lỗi đọc file. Vui lòng thử lại.' });
      };
      
      reader.readAsText(file);
    } catch (err) {
      console.error('Error handling backup upload:', err);
      setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi tải file. Vui lòng thử lại.' });
    }
  };

  // Xử lý xóa tất cả dữ liệu
  const handleClearData = async () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu lịch? Thao tác này không thể hoàn tác.')) {
      try {
        // Sao lưu dữ liệu hiện tại trước khi xóa
        setBackupData([...lichData]);
        
        const response = await fetch('/api/schedule', {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
          setLichData([]);
          
          if (result.lastUpdated) {
            setLastUpdated(new Date(result.lastUpdated));
          }
          
          setUploadStatus({ type: 'success', message: 'Đã xóa tất cả dữ liệu. Bạn có thể khôi phục lại trong phần Sao lưu & Khôi phục.' });
          setTimeout(() => setUploadStatus(null), 5000);
        } else {
          setUploadStatus({ type: 'error', message: 'Lỗi khi xóa dữ liệu. Vui lòng thử lại.' });
        }
      } catch (error) {
        console.error('Error clearing data:', error);
        setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi xóa dữ liệu. Vui lòng thử lại.' });
      }
    }
  };

  // Xử lý khôi phục từ sao lưu tạm thời
  const handleRestoreFromTemp = async () => {
    if (!backupData || backupData.length === 0) {
      setUploadStatus({ type: 'error', message: 'Không có dữ liệu sao lưu để khôi phục.' });
      return;
    }
    
    try {
      // Lưu dữ liệu sao lưu vào server
      const saveSuccess = await saveDataToServer(backupData);
      
      if (saveSuccess) {
        // Cập nhật state
        setLichData(backupData);
        
        setUploadStatus({ type: 'success', message: 'Khôi phục dữ liệu thành công!' });
        setTimeout(() => setUploadStatus(null), 3000);
      } else {
        setUploadStatus({ type: 'error', message: 'Lỗi khi khôi phục dữ liệu. Vui lòng thử lại.' });
      }
    } catch (error) {
      console.error('Error restoring from temp backup:', error);
      setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi khôi phục dữ liệu. Vui lòng thử lại.' });
    }
  };

  // Xử lý tạo sao lưu
  const handleCreateBackup = () => {
    try {
      // Tạo file sao lưu dạng JSON
      const backupData = JSON.stringify(lichData, null, 2);
      const blob = new Blob([backupData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // Tạo link để tải về
      const link = document.createElement('a');
      link.href = url;
      link.download = `Lich_Hoat_Dong_Sao_Luu_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setUploadStatus({ type: 'success', message: 'Đã tạo file sao lưu!' });
      setTimeout(() => setUploadStatus(null), 3000);
    } catch (error) {
      console.error('Error creating backup:', error);
      setUploadStatus({ type: 'error', message: 'Lỗi khi tạo file sao lưu. Vui lòng thử lại.' });
    }
  };

  // Xử lý xuất dữ liệu ra file Excel
  const handleExportData = () => {
    try {
      // Chuyển đổi dữ liệu trở lại định dạng gốc
      const exportData = lichData.map(item => ({
        'ngay lich': item.ngayLich,
        'buoi lich': item.buoiLich,
        'loai an': item.loaiAn,
        'phong xu': item.phongXu,
        'nguyen don': item.nguyenDon,
        'bi don/so bc': item.biDon,
        'viec kien': item.viecKien,
        'ho ten': item.hoTen,
        'noi xu': item.noiXu
      }));
      
      // Tạo workbook mới
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      
      // Thêm worksheet vào workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'LichHoatDong');
      
      // Xuất file
      XLSX.writeFile(workbook, `Lich_Hoat_Dong_Toa_An_${new Date().toISOString().split('T')[0]}.xlsx`);
      
      setUploadStatus({ type: 'success', message: 'Xuất dữ liệu thành công!' });
      setTimeout(() => setUploadStatus(null), 3000);
    } catch (error) {
      console.error('Error exporting data:', error);
      setUploadStatus({ type: 'error', message: 'Lỗi khi xuất dữ liệu. Vui lòng thử lại.' });
    }
  };

  // Xử lý chỉnh sửa một bản ghi
  const handleEdit = (item) => {
    setEditingItem(item);
    setEditFormData({...item});
    setShowEditModal(true);
  };

  // Xử lý lưu chỉnh sửa
  const handleSaveEdit = async () => {
    try {
      // Cập nhật item trong mảng dữ liệu
      const updatedData = lichData.map(item => 
        item.id === editingItem.id ? editFormData : item
      );
      
      // Lưu dữ liệu vào server
      const saveSuccess = await saveDataToServer(updatedData);
      
      if (saveSuccess) {
        // Cập nhật state
        setLichData(updatedData);
        setShowEditModal(false);
        setEditingItem(null);
        
        setUploadStatus({ type: 'success', message: 'Cập nhật thành công!' });
        setTimeout(() => setUploadStatus(null), 3000);
      } else {
        setUploadStatus({ type: 'error', message: 'Lỗi khi lưu dữ liệu. Vui lòng thử lại.' });
      }
    } catch (error) {
      console.error('Error updating data:', error);
      setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi cập nhật. Vui lòng thử lại.' });
    }
  };

  // Xử lý thêm mới bản ghi
  const handleAddNew = () => {
    setNewItemData({
      ngayLich: '',
      buoiLich: 'Sáng',
      loaiAn: '',
      phongXu: '',
      nguyenDon: '',
      biDon: '',
      viecKien: '',
      hoTen: '',
      noiXu: ''
    });
    setShowAddModal(true);
  };

  // Xử lý lưu bản ghi mới
  const handleSaveNew = async () => {
    try {
      // Xác thực dữ liệu
      if (!newItemData.ngayLich) {
        setUploadStatus({ type: 'error', message: 'Vui lòng nhập ngày lịch.' });
        return;
      }
      
      // Tạo bản ghi mới với ID duy nhất
      const newItem = {
        ...newItemData,
        id: `item-${Date.now()}`
      };
      
      // Thêm vào mảng dữ liệu
      const updatedData = [...lichData, newItem];
      
      // Lưu dữ liệu vào server
      const saveSuccess = await saveDataToServer(updatedData);
      
      if (saveSuccess) {
        // Cập nhật state
        setLichData(updatedData);
        setShowAddModal(false);
        
        setUploadStatus({ type: 'success', message: 'Thêm mới thành công!' });
        setTimeout(() => setUploadStatus(null), 3000);
      } else {
        setUploadStatus({ type: 'error', message: 'Lỗi khi lưu dữ liệu. Vui lòng thử lại.' });
      }
    } catch (error) {
      console.error('Error adding new data:', error);
      setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi thêm mới. Vui lòng thử lại.' });
    }
  };

  // Xử lý thay đổi giá trị trong form edit
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Xử lý thay đổi giá trị trong form thêm mới
  const handleNewInputChange = (e) => {
    const { name, value } = e.target;
    setNewItemData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Xử lý xóa một bản ghi
  const handleDelete = async (itemId) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa bản ghi này?')) {
      try {
        // Lọc bỏ item cần xóa
        const updatedData = lichData.filter(item => item.id !== itemId);
        
        // Lưu dữ liệu vào server
        const saveSuccess = await saveDataToServer(updatedData);
        
        if (saveSuccess) {
          // Cập nhật state
          setLichData(updatedData);
          
          setUploadStatus({ type: 'success', message: 'Xóa thành công!' });
          setTimeout(() => setUploadStatus(null), 3000);
        } else {
          setUploadStatus({ type: 'error', message: 'Lỗi khi lưu dữ liệu. Vui lòng thử lại.' });
        }
      } catch (error) {
        console.error('Error deleting data:', error);
        setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi xóa. Vui lòng thử lại.' });
      }
    }
  };

  // Xử lý sắp xếp dữ liệu
  const handleSort = (field) => {
    if (sortField === field) {
      // Đảo ngược hướng sắp xếp nếu đang sắp xếp theo field này
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Đổi field sắp xếp và reset hướng sắp xếp về asc
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Hàm sắp xếp dữ liệu
  const sortData = (data) => {
    return [...data].sort((a, b) => {
      let valueA, valueB;
      
      // Xử lý đặc biệt cho trường ngày
      if (sortField === 'ngayLich') {
        valueA = getDateValue(a[sortField]);
        valueB = getDateValue(b[sortField]);
      } else {
        valueA = a[sortField] ? a[sortField].toString().toLowerCase() : '';
        valueB = b[sortField] ? b[sortField].toString().toLowerCase() : '';
      }
      
      if (sortDirection === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
  };

  // Xử lý click ra ngoài modal để đóng
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShowEditModal(false);
      }
      if (addModalRef.current && !addModalRef.current.contains(event.target)) {
        setShowAddModal(false);
      }
    };
    
    // Thêm event listener
    document.addEventListener('mousedown', handleClickOutside);
    
    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Load dữ liệu khi component được mount
  useEffect(() => {
    const initData = async () => {
      try {
        // Lấy dữ liệu từ server
        await fetchDataFromServer();
      } catch (err) {
        console.error('Error initializing data:', err);
        setError('Không thể khởi tạo dữ liệu. Vui lòng tải lại trang.');
      }
    };

    initData();
    
    // Kiểm tra URL để quyết định hiển thị chế độ admin hay không
    if (typeof window !== 'undefined') {
      const currentUrl = window.location.href;
      if (currentUrl.includes("/secret/schedule/manage") || currentUrl.includes("/secret/schedule/import")) {
        setIsAdminView(true);
      }
    }
  }, []);

  // Lọc dữ liệu theo loại án, từ khóa tìm kiếm và ngày
  const filteredData = lichData.filter(item => {
    // Lọc theo loại án
    const matchType = filterType === 'all' || item.loaiAn === filterType;
    
    // Lọc theo từ khóa tìm kiếm
    const matchSearch = searchTerm === '' || 
      Object.values(item).some(val => 
        val && val.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    // Lọc theo ngày
    let matchDate = true;
    if (dateFilter) {
      const itemDate = parseDate(item.ngayLich);
      const filterDate = parseDate(dateFilter);
      
      if (itemDate && filterDate) {
        matchDate = (
itemDate.getDate() === filterDate.getDate() && 
          itemDate.getMonth() === filterDate.getMonth() && 
          itemDate.getFullYear() === filterDate.getFullYear()
        );
      } else {
        // Nếu không thể parse ngày, thử so sánh chuỗi
        matchDate = item.ngayLich.includes(dateFilter);
      }
    }
    
    return matchType && matchSearch && matchDate;
  });

  // Sắp xếp dữ liệu
  const sortedData = sortData(filteredData);

  // Phân trang
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedData.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  if (loading) {
    return (
      <div className="container p-4 mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 border-t-2 border-b-2 rounded-full animate-spin border-blue-500"></div>
            <p className="text-lg">Đang tải dữ liệu...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container p-4 mx-auto">
        <div className="p-4 text-red-500">{error}</div>
      </div>
    );
  }

  // Lấy danh sách các loại án độc nhất
  const uniqueTypes = ['all', ...new Set(lichData.map(item => item.loaiAn).filter(Boolean))];

  // Tạo biểu tượng sắp xếp
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    
    return sortDirection === 'asc' 
      ? <span className="ml-1">↑</span> 
      : <span className="ml-1">↓</span>;
  };

  return (
    <div className="container p-4 mx-auto">
      <h1 className="mb-6 text-2xl font-bold text-center">Lịch Hoạt Động Tòa Án</h1>
      
      {/* Khu vực Admin */}
      {isAdminView && (
        <div className="p-4 mb-8 border-2 rounded-lg bg-yellow-50 border-yellow-500">
          <h2 className="mb-3 text-xl font-bold text-yellow-800">Quản Lý Lịch Hoạt Động</h2>
          
          {/* Tab điều hướng */}
          <div className="flex mb-4 border-b border-yellow-300">
            <button 
              className={`px-4 py-2 font-medium ${adminTab === 'upload' 
                ? 'bg-white text-yellow-800 rounded-t border-t border-l border-r border-yellow-300' 
                : 'text-yellow-700 hover:bg-yellow-100'}`}
              onClick={() => setAdminTab('upload')}
            >
              Nhập & Xuất Dữ Liệu
            </button>
            <button 
              className={`px-4 py-2 font-medium ${adminTab === 'add' 
                ? 'bg-white text-yellow-800 rounded-t border-t border-l border-r border-yellow-300' 
                : 'text-yellow-700 hover:bg-yellow-100'}`}
              onClick={() => setAdminTab('add')}
            >
              Thêm Mới
            </button>
            <button 
              className={`px-4 py-2 font-medium ${adminTab === 'backup' 
                ? 'bg-white text-yellow-800 rounded-t border-t border-l border-r border-yellow-300' 
                : 'text-yellow-700 hover:bg-yellow-100'}`}
              onClick={() => setAdminTab('backup')}
            >
              Sao Lưu & Khôi Phục
            </button>
          </div>
          
          {/* Tab Nhập & Xuất Dữ Liệu */}
          {adminTab === 'upload' && (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Tải lên file Excel mới */}
              <div className="p-3 bg-white border rounded">
                <h3 className="mb-2 font-semibold">Tải lên file Excel mới</h3>
                <div className="flex items-center space-x-2">
                  <input 
                    type="file" 
                    ref={fileInputRef}
                    accept=".xlsx, .xls" 
                    onChange={handleFileUpload}
                    className="flex-1"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">Hỗ trợ định dạng .xlsx, .xls</p>
              </div>
              
              {/* Quản lý dữ liệu */}
              <div className="p-3 bg-white border rounded">
                <h3 className="mb-2 font-semibold">Quản lý dữ liệu</h3>
                <div className="flex flex-wrap gap-2">
                  <button 
                    className="px-3 py-1 text-white bg-blue-500 rounded hover:bg-blue-600"
                    onClick={handleExportData}
                  >
                    Xuất dữ liệu Excel
                  </button>
                  <button 
                    className="px-3 py-1 text-white bg-red-500 rounded hover:bg-red-600"
                    onClick={handleClearData}
                  >
                    Xóa tất cả dữ liệu
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {/* Tab Thêm Mới */}
          {adminTab === 'add' && (
            <div className="p-3 bg-white border rounded">
              <h3 className="mb-2 font-semibold">Thêm bản ghi mới</h3>
              <p className="mb-2 text-sm text-gray-600">
                Nhập thông tin bản ghi mới trực tiếp từ giao diện:
              </p>
              <button 
                className="px-3 py-1 text-white bg-green-500 rounded hover:bg-green-600"
                onClick={handleAddNew}
              >
                Thêm bản ghi mới
              </button>
            </div>
          )}
          
          {/* Tab Sao Lưu & Khôi Phục */}
          {adminTab === 'backup' && (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Sao lưu dữ liệu */}
              <div className="p-3 bg-white border rounded">
                <h3 className="mb-2 font-semibold">Sao lưu dữ liệu</h3>
                <p className="mb-2 text-sm text-gray-600">
                  Tạo bản sao lưu dữ liệu để dự phòng:
                </p>
                <button 
                  className="px-3 py-1 text-white bg-blue-500 rounded hover:bg-blue-600"
                  onClick={handleCreateBackup}
                  disabled={lichData.length === 0}
                >
                  Tạo file sao lưu
                </button>
              </div>
              
              {/* Khôi phục dữ liệu */}
              <div className="p-3 bg-white border rounded">
                <h3 className="mb-2 font-semibold">Khôi phục dữ liệu</h3>
                
                {backupData && backupData.length > 0 && (
                  <div className="mb-3">
                    <p className="mb-1 text-sm text-gray-600">
                      Có dữ liệu sao lưu tạm thời từ lần xóa gần nhất:
                    </p>
                    <button 
                      className="px-3 py-1 text-white bg-orange-500 rounded hover:bg-orange-600"
                      onClick={handleRestoreFromTemp}
                    >
                      Khôi phục ({backupData.length} bản ghi)
                    </button>
                  </div>
                )}
                
                <p className="mb-1 text-sm text-gray-600">
                  Khôi phục từ file sao lưu:
                </p>
                <div className="flex items-center space-x-2">
                  <input 
                    type="file" 
                    ref={backupFileRef}
                    accept=".json" 
                    onChange={handleBackupUpload}
                    className="flex-1"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">Chỉ hỗ trợ file .json được tạo từ chức năng sao lưu</p>
              </div>
            </div>
          )}
          
          {/* Thông báo trạng thái */}
          {uploadStatus && (
            <div className={`mt-3 p-2 rounded ${
              uploadStatus.type === 'success' ? 'bg-green-100 text-green-800' : 
              uploadStatus.type === 'error' ? 'bg-red-100 text-red-800' : 
              'bg-blue-100 text-blue-800'
            }`}>
              {uploadStatus.message}
            </div>
          )}
          
          <div className="mt-3 text-xs text-gray-500">
            {lastUpdated && (
              <p>Dữ liệu được cập nhật lần cuối: {formatDate(lastUpdated)} {lastUpdated.toLocaleTimeString()}</p>
            )}
            <p className="mt-1">Dữ liệu được lưu trữ trên server và sẽ được đồng bộ trên tất cả các trình duyệt.</p>
          </div>
        </div>
      )}
      
      {/* Bộ lọc và tìm kiếm */}
      <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-3">
        <div>
          <label className="block mb-2 font-medium">Loại án:</label>
          <select 
            className="w-full p-2 border rounded"
            value={filterType}
            onChange={(e) => {
              setFilterType(e.target.value);
              setCurrentPage(1);
            }}
          >
            {uniqueTypes.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'Tất cả' : type}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block mb-2 font-medium">Tìm kiếm:</label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
        
        <div>
          <label className="block mb-2 font-medium">Ngày (DD/MM/YYYY):</label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            placeholder="VD: 10/11/2022"
            value={dateFilter}
            onChange={(e) => {
              setDateFilter(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
      </div>
      
      {/* Điều chỉnh số bản ghi trên trang */}
      <div className="flex justify-between mb-3">
        <div className="flex items-center space-x-2">
          <label className="font-medium">Hiển thị:</label>
          <select 
            className="p-1 border rounded"
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          <span className="text-gray-600">bản ghi</span>
        </div>
        
        {isAdminView && (
          <button 
            className="px-3 py-1 text-white bg-green-500 rounded hover:bg-green-600"
            onClick={handleAddNew}
          >
            Thêm mới
          </button>
        )}
      </div>
      
      {/* Bảng dữ liệu */}
      <div className="mb-6 overflow-x-auto">
        <table className="min-w-full bg-white border rounded shadow">
          <thead className="bg-gray-100">
            <tr>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('ngayLich')}
              >
                Ngày {getSortIcon('ngayLich')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('buoiLich')}
              >
                Buổi {getSortIcon('buoiLich')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('loaiAn')}
              >
                Loại án {getSortIcon('loaiAn')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('phongXu')}
              >
                Phòng xử {getSortIcon('phongXu')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('nguyenDon')}
              >
                Nguyên đơn {getSortIcon('nguyenDon')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('biDon')}
              >
                Bị đơn {getSortIcon('biDon')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('viecKien')}
              >
                Việc kiện {getSortIcon('viecKien')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('hoTen')}
              >
                Người xử {getSortIcon('hoTen')}
              </th>
              <th 
                className="p-3 text-left border-b cursor-pointer hover:bg-gray-200"
                onClick={() => handleSort('noiXu')}
              >
                Nơi xử {getSortIcon('noiXu')}
              </th>
              {isAdminView && <th className="p-3 text-center border-b">Thao tác</th>}
            </tr>
          </thead>
          <tbody>
            {currentItems.length > 0 ? (
              currentItems.map((item, index) => (
                <tr key={item.id || index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="p-3 border-b">{formatDate(item.ngayLich)}</td>
                  <td className="p-3 border-b">{item.buoiLich}</td>
                  <td className="p-3 border-b">{item.loaiAn}</td>
                  <td className="p-3 border-b">{item.phongXu}</td>
                  <td className="p-3 border-b">{item.nguyenDon}</td>
                  <td className="p-3 border-b">{item.biDon}</td>
                  <td className="p-3 border-b">{item.viecKien}</td>
                  <td className="p-3 border-b">{item.hoTen}</td>
                  <td className="p-3 border-b">{item.noiXu}</td>
                  {isAdminView && (
                    <td className="p-3 text-center border-b">
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          onClick={() => handleEdit(item)}
                          className="p-1 text-white bg-blue-500 rounded hover:bg-blue-600"
                          title="Chỉnh sửa"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDelete(item.id)}
                          className="p-1 text-white bg-red-500 rounded hover:bg-red-600"
                          title="Xóa"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v1m5 0V4" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={isAdminView ? "10" : "9"} className="p-3 text-center">Không tìm thấy dữ liệu nào</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      {/* Phân trang */}
      {sortedData.length > 0 && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-500">
            Hiển thị {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, sortedData.length)} trên tổng số {sortedData.length} bản ghi
          </div>
          
          <div className="flex gap-2">
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => setCurrentPage(prevPage => Math.max(prevPage - 1, 1))}
              disabled={currentPage === 1}
            >
              Trước
            </button>
            
            {/* Hiển thị các nút trang */}
            <div className="flex gap-1">
              {[...Array(Math.min(5, totalPages))].map((_, idx) => {
                let pageNum;
                
                // Tính toán số trang cần hiển thị
                if (totalPages <= 5) {
                  pageNum = idx + 1;
                } else if (currentPage <= 3) {
                  pageNum = idx + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + idx;
                } else {
                  pageNum = currentPage - 2 + idx;
                }
                
                if (pageNum > 0 && pageNum <= totalPages) {
                  return (
                    <button
                      key={pageNum}
                      className={`px-3 py-1 border rounded ${
                        currentPage === pageNum ? 'bg-blue-500 text-white' : ''
                      }`}
                      onClick={() => setCurrentPage(pageNum)}
                    >
                      {pageNum}
                    </button>
                  );
                }
                return null;
              })}
            </div>
            
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => setCurrentPage(prevPage => Math.min(prevPage + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Sau
            </button>
          </div>
        </div>
      )}
      
      {/* Tổng kết */}
      <div className="p-3 mt-6 border rounded bg-blue-50 border-blue-200">
        <div className="flex items-center justify-between">
          <p>Tổng số: <strong>{sortedData.length}</strong> bản ghi</p>
          {lastUpdated && !isAdminView && (
            <p className="text-sm text-gray-600">
              Cập nhật lần cuối: {formatDate(lastUpdated)}
            </p>
          )}
        </div>
      </div>
      
      {/* Modal chỉnh sửa */}
      {showEditModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div ref={modalRef} className="w-full max-w-3xl p-6 mx-4 bg-white rounded-lg shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold">Chỉnh sửa thông tin</h3>
              <button 
                onClick={() => setShowEditModal(false)}
                className="p-1 text-gray-500 rounded hover:bg-gray-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="block mb-1 font-medium">Ngày:</label>
                <input
                  type="text"
                  name="ngayLich"
                  value={editFormData.ngayLich || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="DD-MMM-YY (10-Nov-22)"
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Buổi:</label>
                <select
                  name="buoiLich"
                  value={editFormData.buoiLich || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                >
                  <option value="">Chọn buổi</option>
                  <option value="Sáng">Sáng</option>
                  <option value="Chiều">Chiều</option>
                </select>
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Loại án:</label>
                <input
                  type="text"
                  name="loaiAn"
                  value={editFormData.loaiAn || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="KTST, HCST, v.v."
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Phòng xử:</label>
                <input
                  type="text"
                  name="phongXu"
                  value={editFormData.phongXu || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Số phòng"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block mb-1 font-medium">Nguyên đơn:</label>
                <input
                  type="text"
                  name="nguyenDon"
                  value={editFormData.nguyenDon || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Tên nguyên đơn"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block mb-1 font-medium">Bị đơn:</label>
                <input
                  type="text"
                  name="biDon"
                  value={editFormData.biDon || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Tên bị đơn"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block mb-1 font-medium">Việc kiện:</label>
                <input
                  type="text"
                  name="viecKien"
                  value={editFormData.viecKien || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Mô tả việc kiện"
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Người xử:</label>
                <input
                  type="text"
                  name="hoTen"
                  value={editFormData.hoTen || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Họ tên người xử"
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Nơi xử:</label>
                <input
                  type="text"
                  name="noiXu"
                  value={editFormData.noiXu || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Địa điểm xử"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
              >
                Hủy
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
              >
                Lưu thay đổi
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Modal thêm mới */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div ref={addModalRef} className="w-full max-w-3xl p-6 mx-4 bg-white rounded-lg shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold">Thêm bản ghi mới</h3>
              <button 
                onClick={() => setShowAddModal(false)}
                className="p-1 text-gray-500 rounded hover:bg-gray-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="block mb-1 font-medium">Ngày:</label>
                <input
                  type="text"
                  name="ngayLich"
                  value={newItemData.ngayLich || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="DD-MMM-YY (10-Nov-22)"
                  required
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Buổi:</label>
                <select
                  name="buoiLich"
                  value={newItemData.buoiLich || 'Sáng'}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  required
                >
                  <option value="Sáng">Sáng</option>
                  <option value="Chiều">Chiều</option>
                </select>
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Loại án:</label>
                <input
                  type="text"
                  name="loaiAn"
                  value={newItemData.loaiAn || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="KTST, HCST, v.v."
                  required
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Phòng xử:</label>
                <input
                  type="text"
                  name="phongXu"
                  value={newItemData.phongXu || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Số phòng"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block mb-1 font-medium">Nguyên đơn:</label>
                <input
                  type="text"
                  name="nguyenDon"
                  value={newItemData.nguyenDon || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Tên nguyên đơn"
                  required
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block mb-1 font-medium">Bị đơn:</label>
                <input
                  type="text"
                  name="biDon"
                  value={newItemData.biDon || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Tên bị đơn"
                  required
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block mb-1 font-medium">Việc kiện:</label>
                <input
                  type="text"
                  name="viecKien"
                  value={newItemData.viecKien || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Mô tả việc kiện"
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Người xử:</label>
                <input
                  type="text"
                  name="hoTen"
                  value={newItemData.hoTen || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Họ tên người xử"
                />
              </div>
              
              <div>
                <label className="block mb-1 font-medium">Nơi xử:</label>
                <input
                  type="text"
                  name="noiXu"
                  value={newItemData.noiXu || ''}
                  onChange={handleNewInputChange}
                  className="w-full p-2 border rounded"
                  placeholder="Địa điểm xử"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
              >
                Hủy
              </button>
              <button
                onClick={handleSaveNew}
                className="px-4 py-2 text-white bg-green-500 rounded hover:bg-green-600"
              >
                Thêm mới
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}