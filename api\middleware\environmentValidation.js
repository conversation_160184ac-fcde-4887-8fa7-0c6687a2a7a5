/**
 * Environment validation middleware
 * Ensures all required environment variables are present and valid
 */

const requiredEnvVars = [
  'MONGO_URL',
  'JWT_SECRET',
  'NODE_ENV'
];

const optionalEnvVars = [
  'EMAIL_HOST',
  'EMAIL_PORT',
  'EMAIL_USER',
  'EMAIL_PASS',
  'ADMIN_IP_WHITELIST',
  'CORS_ORIGINS',
  'RATE_LIMIT_WINDOW_MS',
  'RATE_LIMIT_MAX'
];

const validateEnvironment = () => {
  const missing = [];
  const warnings = [];
  // Check required environment variables
  requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  });

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    process.exit(1);
  }

  // Validate JWT_SECRET strength
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    warnings.push('JWT_SECRET should be at least 32 characters long for security');
  }

  // Validate NODE_ENV
  const validEnvs = ['development', 'production', 'test'];
  if (!validEnvs.includes(process.env.NODE_ENV)) {
    warnings.push(`NODE_ENV should be one of: ${validEnvs.join(', ')}`);
  }

  // Check optional but recommended environment variables
  optionalEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      warnings.push(`Optional environment variable ${varName} is not set`);
    }
  });

  // Validate MONGO_URL format
  if (process.env.MONGO_URL && !process.env.MONGO_URL.startsWith('mongodb')) {
    console.error('❌ MONGO_URL should start with mongodb:// or mongodb+srv://');
    process.exit(1);
  }

  // Display warnings
  if (warnings.length > 0) {
    console.warn('⚠️  Environment warnings:');
    warnings.forEach(warning => {
      console.warn(`   - ${warning}`);
    });
  }

  console.log('✅ Environment validation passed');
};

// Security-focused environment defaults
const getSecureDefaults = () => {
  return {
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: process.env.PORT || 3001,
    
    // Rate limiting defaults
    RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    RATE_LIMIT_MAX: parseInt(process.env.RATE_LIMIT_MAX) || 100,
    
    // Security headers
    FORCE_HTTPS: process.env.FORCE_HTTPS === 'true' || process.env.NODE_ENV === 'production',
    
    // CORS origins
    CORS_ORIGINS: process.env.CORS_ORIGINS ? 
      process.env.CORS_ORIGINS.split(',').map(origin => origin.trim()) :
      ['http://localhost:3000', 'http://localhost:8000'],
    
    // Admin IP whitelist
    ADMIN_IP_WHITELIST: process.env.ADMIN_IP_WHITELIST ? 
      process.env.ADMIN_IP_WHITELIST.split(',').map(ip => ip.trim()) : [],
    
    // File upload limits
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    MAX_FILES: parseInt(process.env.MAX_FILES) || 10,
    
    // Session security
    SESSION_TIMEOUT: parseInt(process.env.SESSION_TIMEOUT) || 24 * 60 * 60 * 1000, // 24 hours
    
    // Database security
    DB_CONNECTION_TIMEOUT: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 30000, // 30 seconds
    DB_MAX_POOL_SIZE: parseInt(process.env.DB_MAX_POOL_SIZE) || 10,
    
    // Logging
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    ENABLE_SECURITY_LOGS: process.env.ENABLE_SECURITY_LOGS !== 'false'
  };
};

module.exports = {
  validateEnvironment,
  getSecureDefaults,
  requiredEnvVars,
  optionalEnvVars
};
