import http from "@/lib/http";
import envConfig from "@/config";

export interface DashboardStats {
  totalPosts: number;
  totalUsers: number;
  totalPages: number;
  totalViews: number;
  pendingPosts: number;
  activeUsers: number;
  analytics: {
    totalPageViews: number;
    homeViewsToday: number;
    postViewsToday: number;
    totalViewsToday: number;
    weeklyViews: number;
    uniqueVisitorsToday: number;
    homeViewsGrowth: number;
    postViewsGrowth: number;
  };
}

export interface DashboardStatsResponse {
  success: boolean;
  stats: DashboardStats;
}

export interface RecentActivity {
  id: string;
  type: 'post_created' | 'post_updated' | 'post_pending' | 'post_pending_updated' | 'user_registered' | 'page_created' | 'page_updated';
  action: string;
  title: string;
  slug?: string;
  user: string;
  userId: string;
  time: string;
  status?: string;
  role?: string;
  isActive?: boolean;
}

export interface RecentActivitiesResponse {
  success: boolean;
  activities: RecentActivity[];
}

export interface PendingPost {
  _id: string;
  title: string;
  slug: string;
  short: string;
  createdAt: string;
  updatedAt: string;
  user: {
    _id: string;
    username: string;
    email: string;
  };
  categories: Array<{
    _id: string;
    name: string;
    slug: string;
  }>;
  isActive: boolean;
  isFeature: boolean;
  views: number;
}

export interface PendingPostsResponse {
  success: boolean;
  posts: PendingPost[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalPosts: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

const adminApiRequest = {
  getDashboardStats: (sessionToken: string) =>
    http.get<DashboardStatsResponse>("/api/administrator/dashboard-stats", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  getRecentActivities: (sessionToken: string, limit?: number) =>
    http.get<RecentActivitiesResponse>(`/api/administrator/recent-activities${limit ? `?limit=${limit}` : ''}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  getPendingPosts: (sessionToken: string, page?: number, limit?: number) =>
    http.get<PendingPostsResponse>(`/api/administrator/pending-posts?page=${page || 1}&limit=${limit || 10}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default adminApiRequest;
