const mongoose = require('mongoose');
const Post = require('../api/models/post');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/blog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Fix video data format to ensure consistency
const fixVideoDataFormat = async () => {
  try {
    console.log('🔧 Fixing video data format...');
    
    const posts = await Post.find({
      video: { $exists: true, $ne: [] }
    });
    
    console.log(`Found ${posts.length} posts with videos to check`);
    let fixedCount = 0;
    
    for (const post of posts) {
      console.log(`\n📝 Processing post: ${post.title}`);
      console.log(`Post ID: ${post._id}`);
      console.log(`Current video field:`, JSON.stringify(post.video));
      
      let needsUpdate = false;
      let normalizedVideo = [];
      
      if (post.video) {
        if (typeof post.video === 'string') {
          // Convert string to array
          normalizedVideo = [post.video];
          needsUpdate = true;
          console.log(`Converting string to array: ${post.video}`);
        } else if (Array.isArray(post.video)) {
          // Process array elements
          normalizedVideo = post.video.map(item => {
            if (typeof item === 'string') {
              return item;
            } else if (typeof item === 'object' && item !== null) {
              // Handle object format like {"0": "url"}
              if (item['0']) {
                return item['0'];
              } else {
                // Get first value from object
                const values = Object.values(item);
                return values[0] || '';
              }
            }
            return '';
          }).filter(url => url); // Remove empty strings
          
          // Check if normalization changed anything
          if (JSON.stringify(normalizedVideo) !== JSON.stringify(post.video)) {
            needsUpdate = true;
            console.log(`Normalizing array:`, normalizedVideo);
          }
        }
        
        if (needsUpdate) {
          post.video = normalizedVideo;
          await post.save();
          console.log(`✅ Fixed video format for: ${post.title}`);
          console.log(`New video field:`, JSON.stringify(normalizedVideo));
          fixedCount++;
        } else {
          console.log(`⏭️ Video format already correct`);
        }
      }
    }
    
    console.log(`\n✅ Fixed ${fixedCount} posts with video format issues`);
  } catch (error) {
    console.error('❌ Error fixing video data format:', error);
  }
};

// List all posts with videos and their formats
const listVideoFormats = async () => {
  try {
    console.log('📋 Listing video data formats...');
    
    const posts = await Post.find({
      video: { $exists: true, $ne: [] }
    }).select('_id title video');
    
    console.log(`Found ${posts.length} posts with videos:`);
    
    posts.forEach((post, index) => {
      console.log(`\n${index + 1}. ${post.title}`);
      console.log(`   ID: ${post._id}`);
      console.log(`   Video type: ${typeof post.video}`);
      console.log(`   Video value: ${JSON.stringify(post.video)}`);
      
      if (Array.isArray(post.video)) {
        console.log(`   Array length: ${post.video.length}`);
        post.video.forEach((item, i) => {
          console.log(`   [${i}] Type: ${typeof item}, Value: ${JSON.stringify(item)}`);
        });
      }
    });
  } catch (error) {
    console.error('❌ Error listing video formats:', error);
  }
};

// Main function
const main = async () => {
  console.log('🚀 Starting video data format fix script...');
  
  await connectDB();
  
  // List current formats
  await listVideoFormats();
  
  console.log('\n' + '='.repeat(50));
  
  // Fix formats
  await fixVideoDataFormat();
  
  console.log('\n' + '='.repeat(50));
  
  // List formats after fix
  console.log('\n📋 Video formats after fix:');
  await listVideoFormats();
  
  console.log('\n✅ Video data format fix completed!');
  process.exit(0);
};

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { fixVideoDataFormat, listVideoFormats };
