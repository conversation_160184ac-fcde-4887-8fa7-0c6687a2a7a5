"use client";

import React, { useState, useEffect } from 'react';
import _ from 'lodash';

export default function SchedulePage() {
  const [lichData, setLichData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [sortOrder, setSortOrder] = useState('desc'); // 'desc' for newest first, 'asc' for oldest first
  
  const itemsPerPage = 10;

  // Hàm chuyển đổi định dạng ngày
  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    
    // Nếu dateStr là đối tượng Date, chuyển đổi thành chuỗi
    if (dateStr instanceof Date) {
      return `${String(dateStr.getDate()).padStart(2, '0')}/${String(dateStr.getMonth() + 1).padStart(2, '0')}/${dateStr.getFullYear()}`;
    }
    
    // Nếu là chuỗi, xử lý như trước
    if (typeof dateStr === 'string') {
      try {
        const parts = dateStr.split('-');
        if (parts.length !== 3) return dateStr;
        
        const months = {
          'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
          'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };
        
        const day = parts[0].padStart(2, '0');
        const month = months[parts[1]] || parts[1];
        let year = parts[2];
        if (year.length === 2) {
          year = '20' + year;
        }
        
        return `${day}/${month}/${year}`;
      } catch (e) {
        return dateStr; // Trả về nguyên bản nếu không chuyển đổi được
      }
    }
    
    return String(dateStr); // Trả về dạng chuỗi cho các trường hợp khác
  };

  // Hàm parse ngày từ chuỗi để so sánh
  const parseDate = (dateStr) => {
    if (!dateStr) return null;
    
    try {
      // Nếu là định dạng DD/MM/YYYY
      if (dateStr.includes('/')) {
        const [day, month, year] = dateStr.split('/');
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
      
      // Nếu là định dạng DD-MMM-YY
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts.length !== 3) return null;
        
        const months = {
          'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
          'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
        };
        
        const day = parseInt(parts[0]);
        const month = months[parts[1]];
        let year = parseInt(parts[2]);
        if (year < 100) {
          year = 2000 + year;
        }
        
        if (isNaN(day) || month === undefined || isNaN(year)) return null;
        
        return new Date(year, month, day);
      }
    } catch (e) {
      console.error('Error parsing date:', e);
      return null;
    }
    
    return null;
  };

  // Lấy dữ liệu từ API
  const fetchDataFromServer = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/schedule');
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Trước khi set dữ liệu, thêm parsedDate cho mỗi item để dùng khi sắp xếp
      const itemsWithParsedDates = (data.items || []).map(item => ({
        ...item,
        parsedDate: parseDate(item.ngayLich)
      }));
      
      setLichData(itemsWithParsedDates);
      if (data.lastUpdated) {
        setLastUpdated(new Date(data.lastUpdated));
      }
      
      return itemsWithParsedDates;
    } catch (error) {
      console.error('Error fetching data from server:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Load dữ liệu khi component được mount
  useEffect(() => {
    const initData = async () => {
      try {
        // Lấy dữ liệu từ server
        await fetchDataFromServer();
      } catch (err) {
        console.error('Error initializing data:', err);
        setError('Không thể tải dữ liệu. Vui lòng thử lại sau.');
      }
    };

    initData();
  }, []);

  // Hàm toggle thứ tự sắp xếp
  const toggleSortOrder = () => {
    setSortOrder(prevOrder => prevOrder === 'desc' ? 'asc' : 'desc');
  };

  // Lọc dữ liệu theo loại án, từ khóa tìm kiếm và ngày
  let filteredData = lichData.filter(item => {
    // Lọc theo loại án
    const matchType = filterType === 'all' || item.loaiAn === filterType;
    
    // Lọc theo từ khóa tìm kiếm
    const matchSearch = searchTerm === '' || 
      Object.values(item).some(val => 
        val && val.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    // Lọc theo ngày
    let matchDate = true;
    if (dateFilter) {
      const itemDate = item.parsedDate;
      const filterDate = parseDate(dateFilter);
      
      if (itemDate && filterDate) {
        matchDate = (
          itemDate.getDate() === filterDate.getDate() && 
          itemDate.getMonth() === filterDate.getMonth() && 
          itemDate.getFullYear() === filterDate.getFullYear()
        );
      } else {
        // Nếu không thể parse ngày, thử so sánh chuỗi
        matchDate = item.ngayLich.includes(dateFilter);
      }
    }
    
    return matchType && matchSearch && matchDate;
  });

  // Sắp xếp dữ liệu theo ngày
  filteredData = _.orderBy(
    filteredData,
    // Sắp xếp theo parsedDate, nếu không có thì để vào cuối
    item => item.parsedDate ? item.parsedDate : new Date(0),
    // Thứ tự sắp xếp: desc = mới nhất trước, asc = cũ nhất trước
    [sortOrder]
  );

  // Phân trang
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  if (loading) {
    return (
      <div className="container p-4 mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 border-t-2 border-b-2 rounded-full animate-spin border-blue-500"></div>
            <p className="text-lg">Đang tải dữ liệu...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container p-4 mx-auto">
        <div className="p-4 text-red-500">{error}</div>
      </div>
    );
  }

  // Lấy danh sách các loại án độc nhất
  const uniqueTypes = ['all', ...new Set(lichData.map(item => item.loaiAn).filter(Boolean))];

  return (
    <div className="container p-4 mx-auto">
      <h1 className="mb-6 text-2xl font-bold text-center">Lịch xét xử tòa án nhân dân Hồ Chí Minh</h1>
      
      {/* Bộ lọc và tìm kiếm */}
      <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-3">
        <div>
          <label className="block mb-2 font-medium">Loại án:</label>
          <select 
            className="w-full p-2 border rounded"
            value={filterType}
            onChange={(e) => {
              setFilterType(e.target.value);
              setCurrentPage(1);
            }}
          >
            {uniqueTypes.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'Tất cả' : type}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block mb-2 font-medium">Tìm kiếm:</label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
        
        <div>
          <label className="block mb-2 font-medium">Ngày (DD/MM/YYYY):</label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            placeholder="VD: 10/11/2022"
            value={dateFilter}
            onChange={(e) => {
              setDateFilter(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
      </div>
      
      {/* Bảng dữ liệu */}
      <div className="mb-6 overflow-x-auto">
        <table className="min-w-full bg-white border rounded shadow">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-3 text-left border-b">
                <div className="flex items-center">
                  <span>Ngày</span>
                  <button 
                    onClick={toggleSortOrder}
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    title={sortOrder === 'desc' ? 'Đang sắp xếp mới nhất trước' : 'Đang sắp xếp cũ nhất trước'}
                  >
                    {sortOrder === 'desc' ? '↓' : '↑'}
                  </button>
                </div>
              </th>
              <th className="p-3 text-left border-b">Buổi</th>
              <th className="p-3 text-left border-b">Loại án</th>
              <th className="p-3 text-left border-b">Phòng xử</th>
              <th className="p-3 text-left border-b">Nguyên đơn</th>
              <th className="p-3 text-left border-b">Bị đơn</th>
              <th className="p-3 text-left border-b">Việc kiện</th>
              <th className="p-3 text-left border-b">Người xử</th>
              <th className="p-3 text-left border-b">Nơi xử</th>
            </tr>
          </thead>
          <tbody>
            {currentItems.length > 0 ? (
              currentItems.map((item, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="p-3 border-b">{formatDate(item.ngayLich)}</td>
                  <td className="p-3 border-b">{item.buoiLich}</td>
                  <td className="p-3 border-b">{item.loaiAn}</td>
                  <td className="p-3 border-b">{item.phongXu}</td>
                  <td className="p-3 border-b">{item.nguyenDon}</td>
                  <td className="p-3 border-b">{item.biDon}</td>
                  <td className="p-3 border-b">{item.viecKien}</td>
                  <td className="p-3 border-b">{item.hoTen}</td>
                  <td className="p-3 border-b">{item.noiXu}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="9" className="p-3 text-center">Không tìm thấy dữ liệu nào</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      {/* Phân trang */}
      {filteredData.length > 0 && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-500">
            Hiển thị {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredData.length)} trên tổng số {filteredData.length} bản ghi
          </div>
          
          <div className="flex gap-2">
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => setCurrentPage(prevPage => Math.max(prevPage - 1, 1))}
              disabled={currentPage === 1}
            >
              Trước
            </button>
            
            <span className="px-3 py-1 border">
              {currentPage} / {totalPages}
            </span>
            
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => setCurrentPage(prevPage => Math.min(prevPage + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Sau
            </button>
          </div>
        </div>
      )}
      
      {/* Tổng kết */}
      <div className="p-3 mt-6 border rounded bg-blue-50 border-blue-200">
        <div className="flex items-center justify-between">
          <p>Tổng số: <strong>{filteredData.length}</strong> bản ghi</p>
          {lastUpdated && (
            <p className="text-sm text-gray-600">
              Cập nhật lần cuối: {formatDate(lastUpdated)}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}