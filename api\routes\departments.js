const express = require("express");
const router = express.Router();
const passport = require("passport");
const {
  getAllDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment
} = require("../controllers/department");
const { verifyAdmin } = require("../middleware/is-admin");

// Admin routes for departments
router.get("/", passport.authenticate('user', { session: false }), verifyAdmin, getAllDepartments);
router.post("/", passport.authenticate('user', { session: false }), verifyAdmin, createDepartment);
router.put("/:id", passport.authenticate('user', { session: false }), verifyAdmin, updateDepartment);
router.delete("/:id", passport.authenticate('user', { session: false }), verifyAdmin, deleteDepartment);

module.exports = router;
