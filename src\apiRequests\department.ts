import http from "@/lib/http";

export interface Department {
  id: string;
  name: string;
}

export interface DepartmentListResType {
  success: boolean;
  data: Department[];
  message: string;
}

export interface DepartmentResType {
  success: boolean;
  data: Department;
  message: string;
}

export interface MessageResType {
  success: boolean;
  message: string;
}

const departmentApiRequest = {
  // Get all departments (admin only)
  getDepartments: (sessionToken: string) =>
    http.get<DepartmentListResType>("/api/admin/departments", {
      baseUrl: "", // Use Next.js API routes
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Create new department (admin only)
  createDepartment: (sessionToken: string, name: string) =>
    http.post<DepartmentResType>("/api/admin/departments", { name }, {
      baseUrl: "", // Use Next.js API routes
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Update department (admin only)
  updateDepartment: (sessionToken: string, id: string, name: string) =>
    http.put<DepartmentResType>(`/api/admin/departments/${id}`, { name }, {
      baseUrl: "", // Use Next.js API routes
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete department (admin only)
  deleteDepartment: (sessionToken: string, id: string) =>
    http.delete<MessageResType>(`/api/admin/departments/${id}`, {
      baseUrl: "", // Use Next.js API routes
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default departmentApiRequest;
