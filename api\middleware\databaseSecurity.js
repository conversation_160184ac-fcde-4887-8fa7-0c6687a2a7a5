const mongoose = require('mongoose');
const validator = require('validator');

/**
 * Database security helpers and utilities
 */

// MongoDB query sanitization
const sanitizeQuery = (query) => {
  if (typeof query !== 'object' || query === null) {
    return query;
  }
  
  const sanitized = {};
  
  for (const key in query) {
    if (query.hasOwnProperty(key)) {
      // Prevent NoSQL injection by removing $ operators from user input
      if (key.startsWith('$')) {
        continue; // Skip potentially dangerous operators
      }
      
      const value = query[key];
      
      if (typeof value === 'object' && value !== null) {
        // Recursively sanitize nested objects
        sanitized[key] = sanitizeQuery(value);
      } else if (Array.isArray(value)) {
        // Sanitize array values
        sanitized[key] = value.map(item => 
          typeof item === 'object' ? sanitizeQuery(item) : item
        );
      } else {
        sanitized[key] = value;
      }
    }
  }
  
  return sanitized;
};

// Safe MongoDB ObjectId validation and conversion
const safeObjectId = (id) => {
  if (!id) return null;
  
  try {
    if (mongoose.Types.ObjectId.isValid(id)) {
      return new mongoose.Types.ObjectId(id);
    }
  } catch (error) {
    console.error('Invalid ObjectId:', id);
  }
  
  return null;
};

// Create secure query builder
const createSecureQuery = (conditions = {}) => {
  const sanitized = sanitizeQuery(conditions);
  
  // Convert string IDs to ObjectIds
  if (sanitized._id && typeof sanitized._id === 'string') {
    sanitized._id = safeObjectId(sanitized._id);
  }
  
  if (sanitized.id && typeof sanitized.id === 'string') {
    sanitized._id = safeObjectId(sanitized.id);
    delete sanitized.id;
  }
  
  return sanitized;
};

// Secure pagination helper
const createSecurePagination = (page = 1, limit = 10, maxLimit = 100) => {
  const parsedPage = parseInt(page, 10);
  const parsedLimit = parseInt(limit, 10);
  
  const safePage = Math.max(1, isNaN(parsedPage) ? 1 : parsedPage);
  const safeLimit = Math.min(maxLimit, Math.max(1, isNaN(parsedLimit) ? 10 : parsedLimit));
  const skip = (safePage - 1) * safeLimit;
  
  return {
    page: safePage,
    limit: safeLimit,
    skip
  };
};

// Field validation for database operations
const validateFields = (data, allowedFields) => {
  const validated = {};
  
  for (const field of allowedFields) {
    if (data.hasOwnProperty(field)) {
      validated[field] = data[field];
    }
  }
  
  return validated;
};

// Secure text search helper
const createSecureTextSearch = (searchTerm) => {
  if (!searchTerm || typeof searchTerm !== 'string') {
    return {};
  }
  
  // Escape special regex characters
  const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  // Limit search term length to prevent ReDoS attacks
  const limitedTerm = escapedTerm.substring(0, 100);
  
  return {
    $regex: new RegExp(limitedTerm, 'i')
  };
};

// Database connection security middleware
const secureConnection = () => {
  // Set mongoose security options
  mongoose.set('sanitizeFilter', true); // Enable query sanitization
  mongoose.set('runValidators', true);   // Always run validators
  
  // Additional connection options for security
  const connectionOptions = {
    maxPoolSize: 10,           // Maintain up to 10 socket connections
    serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
    socketTimeoutMS: 45000,    // Close sockets after 45 seconds of inactivity
    bufferMaxEntries: 0        // Disable buffering
  };
  
  return connectionOptions;
};

// Schema security helpers
const addSecurityToSchema = (schema) => {
  // Add automatic timestamps
  schema.set('timestamps', true);
  
  // Remove sensitive fields from JSON output
  schema.set('toJSON', {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.__v;
      return ret;
    }
  });
  
  // Add index on commonly queried fields
  schema.index({ createdAt: -1 });
  schema.index({ updatedAt: -1 });
  
  // Add pre-save middleware for additional security
  schema.pre('save', function(next) {
    // Ensure required security fields are present
    if (this.isNew && !this.createdAt) {
      this.createdAt = new Date();
    }
    this.updatedAt = new Date();
    next();
  });
  
  return schema;
};

// SQL injection prevention for raw queries (if using SQL database)
const escapeSqlString = (str) => {
  if (typeof str !== 'string') return str;
  
  return str.replace(/[\0\x08\x09\x1a\n\r"'\\\%]/g, function (char) {
    switch (char) {
      case '\0':
        return '\\0';
      case '\x08':
        return '\\b';
      case '\x09':
        return '\\t';
      case '\x1a':
        return '\\z';
      case '\n':
        return '\\n';
      case '\r':
        return '\\r';
      case '"':
      case "'":
      case '\\':
      case '%':
        return '\\' + char;
      default:
        return char;
    }
  });
};

// Audit trail helper
const createAuditLog = (action, userId, resourceType, resourceId, changes = {}) => {
  return {
    action,
    userId: safeObjectId(userId),
    resourceType,
    resourceId: safeObjectId(resourceId),
    changes,
    timestamp: new Date()
  };
};

module.exports = {
  sanitizeQuery,
  safeObjectId,
  createSecureQuery,
  createSecurePagination,
  validateFields,
  createSecureTextSearch,
  secureConnection,
  addSecurityToSchema,
  escapeSqlString,
  createAuditLog
};
