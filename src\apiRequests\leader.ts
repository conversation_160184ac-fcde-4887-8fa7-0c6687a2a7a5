import http from "@/lib/http";
import { 
  CreateLeaderBodyType, 
  UpdateLeaderBodyType, 
  LeaderType,
  LeaderListResType,
  LeaderResType,
  MessageResType
} from "@/schemaValidations/leader.schema";

const leaderApiRequest = {
  // Public API - get active leaders
  getPublicLeaders: () => 
    http.get<LeaderListResType>("/api/leaders/public"),

  // Admin API - requires authentication
  getLeaders: (sessionToken: string) =>
    http.get<LeaderListResType>("/api/admin/leaders", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  getLeader: (sessionToken: string, id: string) =>
    http.get<LeaderResType>(`/api/admin/leaders/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  createLeader: (sessionToken: string, body: CreateLeaderBodyType) =>
    http.post<LeaderResType>("/api/admin/leaders", body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  updateLeader: (sessionToken: string, id: string, body: UpdateLeaderBodyType) =>
    http.put<LeaderResType>(`/api/admin/leaders/${id}`, body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  deleteLeader: (sessionToken: string, id: string) =>
    http.delete<MessageResType>(`/api/admin/leaders/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  toggleLeaderStatus: (sessionToken: string, id: string) =>
    http.patch<LeaderResType>(`/api/admin/leaders/${id}/toggle`, {}, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Image upload for leaders
  uploadLeaderImage: (sessionToken: string, formData: FormData) =>
    http.post<{ url: string }>("/api/admin/leaders/upload-image", formData, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Reorder leaders
  reorderLeaders: (sessionToken: string, leaders: { id: string; order: number }[]) =>
    http.put<MessageResType>("/api/admin/leaders/reorder", { leaders }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default leaderApiRequest;
