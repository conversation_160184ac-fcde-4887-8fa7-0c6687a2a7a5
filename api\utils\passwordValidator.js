/**
 * Password validation utility
 * Ensures password meets security requirements:
 * - At least 8 characters long
 * - Contains at least one lowercase letter
 * - Contains at least one uppercase letter  
 * - Contains at least one number
 * - Contains at least one special character
 */

const validatePassword = (password) => {
  // Strong password regex requiring:
  // (?=.*[a-z]) - at least one lowercase letter
  // (?=.*[A-Z]) - at least one uppercase letter
  // (?=.*\d) - at least one number
  // (?=.*[@$!%*?&]) - at least one special character
  // .{8,} - at least 8 characters long
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&]).{8,}$/;
  
  return strongPasswordRegex.test(password);
};

const getPasswordErrorMessage = () => {
  return "Mật khẩu phải chứa ít nhất 8 ký tự, bao gồm: 1 chữ thường, 1 chữ in hoa, 1 số và 1 ký tự đặc biệt (@$!%*?&)";
};

module.exports = {
  validatePassword,
  getPasswordErrorMessage
};
