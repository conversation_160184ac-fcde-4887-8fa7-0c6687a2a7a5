import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const resolvedParams = await params;
    const imagePath = resolvedParams.path.join('/');
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    // Fetch image from backend
    const response = await fetch(`${backendUrl}/images/leaders/${imagePath}`, {
      method: "GET",
    });

    if (!response.ok) {
      return NextResponse.json(
        { message: "Image not found" },
        { status: 404 }
      );
    }

    // Get the image buffer and content type
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get("content-type") || "image/jpeg";

    // Return image with proper headers
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=31536000, immutable",
      },
    });

  } catch (error) {
    console.error("Error serving image:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
