"use client";

import { useEffect, useState } from 'react';

const SafeStyleOverride = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Ensure this only runs on the client side to prevent hydration mismatch
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    // Function to override only ::marker styles without blocking CSS
    const overrideMarkerStyles = () => {
      // Check if already exists
      const existing = document.getElementById('safe-marker-override');
      if (existing) {
        return; // Already applied, no need to reapply
      }

      // Create style element to override ::marker
      const style = document.createElement('style');
      style.id = 'safe-marker-override';
      style.textContent = `
        /* Override user agent stylesheet for ::marker */
        ::marker {
          unicode-bidi: normal !important;
          font-variant-numeric: normal !important;
          text-transform: inherit !important;
          text-indent: inherit !important;
          text-align: inherit !important;
          text-align-last: inherit !important;
          content: none !important;
          display: none !important;
        }

        /* Remove list markers completely */
        ul, ol {
          list-style: none !important;
        }

        ul::marker, ol::marker, li::marker {
          content: none !important;
          display: none !important;
        }

        /* Custom list styles if needed */
        .custom-list {
          list-style: disc !important;
          padding-left: 1.5rem !important;
        }

        .custom-list-ordered {
          list-style: decimal !important;
          padding-left: 1.5rem !important;
        }
      `;

      document.head.appendChild(style);

      hasApplied = true;
    };

    // Apply override immediately
    overrideMarkerStyles();

    // Check periodically but don't reapply if already exists
    const interval = setInterval(overrideMarkerStyles, 5000);

    // Cleanup
    return () => {
      clearInterval(interval);
      const existing = document.getElementById('safe-marker-override');
      if (existing) {
        existing.remove();
      }
    };
  }, [isClient]);

  return null;
};

export default SafeStyleOverride;
