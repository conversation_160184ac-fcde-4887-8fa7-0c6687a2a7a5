"use client";
import React, { useState, useRef } from 'react';
import * as XLSX from 'xlsx';
import Link from 'next/link';

export default function ScheduleImportPage() {
  const [uploadStatus, setUploadStatus] = useState(null);
  const [previewData, setPreviewData] = useState(null);
  const [fileName, setFileName] = useState('');
  const [importing, setImporting] = useState(false);
  const fileInputRef = useRef(null);

  // Chuẩn hóa dữ liệu từ Excel
  const normalizeExcelData = (data) => {
    return data.map(item => {
      // Đảm bảo kiểu dữ liệu an toàn cho tất cả các trường
      let ngayLich = item['ngay lich'] || '';
      
      // Chuyển đổi ngày thành chuỗi nếu là đối tượng Date
      if (ngayLich instanceof Date) {
        ngayLich = `${String(ngayLich.getDate()).padStart(2, '0')}-${['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'][ngayLich.getMonth()]}-${String(ngayLich.getFullYear()).slice(-2)}`;
      }
      
      return {
        ngayLich: ngayLich,
        buoiLich: item['buoi lich'] || '',
        loaiAn: item['loai an'] || '',
        phongXu: item['phong xu'] || '',
        nguyenDon: item['nguyen don'] || '',
        biDon: item['bi don/so bc'] || '',
        viecKien: item['viec kien'] || '',
        hoTen: item['ho ten'] || '',
        noiXu: item['noi xu'] || ''
      };
    });
  };

  // Lưu dữ liệu vào server
  const saveDataToServer = async (data) => {
    try {
      const response = await fetch('/api/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const result = await response.json();
      return true;
    } catch (error) {
      console.error('Error saving data to server:', error);
      return false;
    }
  };

  // Xử lý file khi chọn
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setFileName(file.name);
    setUploadStatus(null);
    setPreviewData(null);

    try {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { cellDates: true });
          
          // Lấy sheet đầu tiên
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // Chuyển đổi dữ liệu
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          // Kiểm tra dữ liệu
          if (jsonData.length === 0) {
            setUploadStatus({ type: 'error', message: 'File không chứa dữ liệu hoặc không đúng định dạng.' });
            return;
          }
          
          // Kiểm tra cấu trúc dữ liệu
          const requiredColumns = ['ngay lich', 'buoi lich', 'loai an'];
          const missingColumns = requiredColumns.filter(col => !Object.keys(jsonData[0]).some(key => key.toLowerCase() === col));
          
          if (missingColumns.length > 0) {
            setUploadStatus({ 
              type: 'error', 
              message: `File thiếu các cột bắt buộc: ${missingColumns.join(', ')}` 
            });
            return;
          }
          
          // Lưu trữ dữ liệu để xem trước
          setPreviewData({
            total: jsonData.length,
            sample: jsonData.slice(0, 5),
            full: jsonData
          });
          
          setUploadStatus({ type: 'success', message: 'File hợp lệ, sẵn sàng để nhập.' });
        } catch (error) {
          console.error('Error processing Excel file:', error);
          setUploadStatus({ type: 'error', message: 'Lỗi xử lý file Excel. Vui lòng kiểm tra định dạng file.' });
        }
      };
      
      reader.onerror = () => {
        setUploadStatus({ type: 'error', message: 'Lỗi đọc file. Vui lòng thử lại.' });
      };
      
      reader.readAsArrayBuffer(file);
    } catch (err) {
      console.error('Error handling file selection:', err);
      setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi đọc file. Vui lòng thử lại.' });
    }
  };

  // Xử lý nhập dữ liệu
  const handleImport = async () => {
    if (!previewData || !previewData.full || previewData.full.length === 0) {
      setUploadStatus({ type: 'error', message: 'Không có dữ liệu để nhập.' });
      return;
    }

    setImporting(true);
    
    try {
      // Chuẩn hóa dữ liệu
      const normalizedData = normalizeExcelData(previewData.full);
      
      // Lưu vào server
      const saveResult = await saveDataToServer(normalizedData);
      
      if (saveResult) {
        setUploadStatus({ type: 'success', message: `Đã nhập thành công ${normalizedData.length} bản ghi.` });
        // Xóa dữ liệu xem trước
        setPreviewData(null);
        // Xóa file đã chọn
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        setFileName('');
      } else {
        setUploadStatus({ type: 'error', message: 'Không thể lưu dữ liệu. Vui lòng thử lại.' });
      }
    } catch (error) {
      console.error('Error importing data:', error);
      setUploadStatus({ type: 'error', message: 'Có lỗi xảy ra khi nhập dữ liệu. Vui lòng thử lại.' });
    } finally {
      setImporting(false);
    }
  };

  // Xử lý hủy
  const handleCancel = () => {
    setPreviewData(null);
    setUploadStatus(null);
    setFileName('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="container p-6 mx-auto">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Nhập Dữ Liệu Lịch Hoạt Động</h1>
          <Link 
            href="/dashboard/manager/schedule" 
            className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
          >
            Xem Lịch
          </Link>
        </div>
        
        <div className="p-6 mb-6 bg-white border rounded-lg shadow-md">
          <h2 className="mb-4 text-xl font-semibold">Tải lên file Excel</h2>
          
          <div className="p-4 mb-6 border-2 border-dashed rounded-md border-gray-300">
            <input
              type="file"
              ref={fileInputRef}
              accept=".xlsx, .xls"
              onChange={handleFileSelect}
              className="w-full"
            />
            <p className="mt-2 text-sm text-gray-500">
              Hỗ trợ định dạng .xlsx, .xls. File Excel cần có các cột: ngay lich, buoi lich, loai an, phong xu, v.v.
            </p>
          </div>
          
          {fileName && (
            <div className="mb-4 p-2 bg-gray-50 border rounded flex items-center justify-between">
              <span className="text-sm font-medium">{fileName}</span>
              <button 
                onClick={handleCancel} 
                className="text-red-500 hover:text-red-700"
                title="Xóa file"
              >
                ✕
              </button>
            </div>
          )}
          
          {uploadStatus && (
            <div className={`p-3 mb-4 rounded ${
              uploadStatus.type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 
              'bg-red-100 text-red-800 border border-red-200'
            }`}>
              {uploadStatus.message}
            </div>
          )}
          
          {previewData && (
            <div className="mt-6">
              <h3 className="mb-2 text-lg font-medium">Xem trước dữ liệu ({previewData.total} bản ghi)</h3>
              
              <div className="mb-4 overflow-x-auto">
                <table className="min-w-full border rounded">
                  <thead className="bg-gray-100">
                    <tr>
                      {Object.keys(previewData.sample[0]).map((header, idx) => (
                        <th key={idx} className="p-2 text-left border-b">
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {previewData.sample.map((row, rowIdx) => (
                      <tr key={rowIdx} className={rowIdx % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                        {Object.values(row).map((cell, cellIdx) => (
                          <td key={cellIdx} className="p-2 border-b">
                            {cell instanceof Date 
                              ? cell.toLocaleDateString() 
                              : String(cell)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="flex gap-3">
                <button
                  onClick={handleImport}
                  disabled={importing}
                  className={`px-4 py-2 text-white rounded ${
                    importing ? 'bg-blue-300' : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {importing ? 'Đang nhập...' : 'Nhập dữ liệu'}
                </button>
                
                <button
                  onClick={handleCancel}
                  disabled={importing}
                  className="px-4 py-2 text-gray-600 bg-gray-200 rounded hover:bg-gray-300"
                >
                  Hủy
                </button>
              </div>
            </div>
          )}
        </div>
        
        <div className="p-5 bg-yellow-50 border rounded border-yellow-200">
          <h3 className="mb-2 text-lg font-semibold text-yellow-800">Hướng dẫn</h3>
          
          <ul className="pl-5 list-disc">
            <li className="mb-1">File Excel cần có các cột sau: 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">ngay lich</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">buoi lich</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">loai an</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">phong xu</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">nguyen don</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">bi don/so bc</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">viec kien</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">ho ten</code>, 
              <code className="px-1 py-0.5 mx-1 bg-gray-100 rounded text-sm">noi xu</code>
            </li>
            <li className="mb-1">Dữ liệu mới sẽ thay thế hoàn toàn dữ liệu cũ</li>
            <li className="mb-1">Dữ liệu được lưu trên server và đồng bộ trên tất cả các trình duyệt</li>
            <li>Để bảo vệ dữ liệu, bạn nên xuất định kỳ dữ liệu ra file Excel</li>
          </ul>
        </div>
      </div>
    </div>
  );
}