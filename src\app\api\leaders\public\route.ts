import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Get backend API URL from environment
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/leaders/public`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      cache: 'no-store' // Disable caching for dynamic content
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi lấy danh sách lãnh đạo" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Lấy danh sách lãnh đạo thành công"
    });

  } catch (error) {
    console.error("Error in leaders public API:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến server" 
      },
      { status: 500 }
    );
  }
}
