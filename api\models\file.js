const mongoose = require('mongoose');

const fileSchema = new mongoose.Schema({
  filename: {
    type: String,
    required: true,
    trim: true
  },
  originalName: {
    type: String,
    required: true,
    trim: true
  },
  mimetype: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  path: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['image', 'video', 'document', 'other'],
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  description: {
    type: String,
    trim: true
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  lastAccessed: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better performance
fileSchema.index({ uploadedBy: 1 });
fileSchema.index({ type: 1 });
fileSchema.index({ isActive: 1 });
fileSchema.index({ createdAt: -1 });
fileSchema.index({ filename: 'text', originalName: 'text', description: 'text' });

// Virtual for file type detection
fileSchema.virtual('fileType').get(function() {
  if (this.mimetype.startsWith('image/')) return 'image';
  if (this.mimetype.startsWith('video/')) return 'video';
  if (this.mimetype.includes('pdf') || this.mimetype.includes('document') || this.mimetype.includes('text')) return 'document';
  return 'other';
});

// Method to format file size
fileSchema.methods.getFormattedSize = function() {
  const bytes = this.size;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Static method to get file statistics
fileSchema.statics.getStats = async function() {
  const totalFiles = await this.countDocuments({ isActive: true });
  const totalSize = await this.aggregate([
    { $match: { isActive: true } },
    { $group: { _id: null, total: { $sum: '$size' } } }
  ]);

  const filesByType = await this.aggregate([
    { $match: { isActive: true } },
    { 
      $group: { 
        _id: '$type', 
        count: { $sum: 1 }, 
        size: { $sum: '$size' } 
      } 
    },
    { $sort: { count: -1 } }
  ]);

  const recentUploads = await this.countDocuments({
    isActive: true,
    createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
  });

  return {
    totalFiles,
    totalSize: totalSize[0]?.total || 0,
    filesByType: filesByType.map(item => ({
      type: item._id,
      count: item.count,
      size: item.size
    })),
    recentUploads
  };
};

// Pre-save middleware to set file type
fileSchema.pre('save', function(next) {
  if (this.mimetype.startsWith('image/')) {
    this.type = 'image';
  } else if (this.mimetype.startsWith('video/')) {
    this.type = 'video';
  } else if (this.mimetype.includes('pdf') || this.mimetype.includes('document') || this.mimetype.includes('text')) {
    this.type = 'document';
  } else {
    this.type = 'other';
  }
  next();
});

module.exports = mongoose.model('File', fileSchema);
