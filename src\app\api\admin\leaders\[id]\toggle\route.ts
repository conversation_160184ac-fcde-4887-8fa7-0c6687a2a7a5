import { NextRequest, NextResponse } from "next/server";

// PATCH - Toggle leader active status (admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/leaders/${id}/toggle`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi thay đổi trạng thái lãnh đạo" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Thay đổi trạng thái lãnh đạo thành công"
    });

  } catch (error) {
    console.error("Error in admin leader toggle:", error);
    return NextResponse.json(
      { success: false, message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}
