"use client";
import { useEffect, useState, use } from "react";
import AddForm from "@/app/(private)/dashboard/secret/page/add/add-form";
import { PageCreate } from "@/schemaValidations/page.schema";
import pageApiRequest from "@/apiRequests/page";
import { toast } from "react-toastify";
import { z } from "zod";
import Revision from "@/components/Widget/Revision";

type PageFormValues = z.infer<typeof PageCreate>[0];

export default function EditPage({ params }: { params: Promise<{ id: any }> }) {
  const [page, setPage] = useState< PageFormValues | null>(null);
  const [sessionToken, setSessionToken] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const resolvedParams = use(params);
  const Id = resolvedParams.id;

  useEffect(() => {
    // Get session token on client side only
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  useEffect(() => {
    if (!sessionToken) return; // Wait for session token to be loaded

    const fetchPage = async () => {
      setLoading(true);
      setError("");

      try {
        const result = await pageApiRequest.fetchPageById(Id, sessionToken);

        if (result.payload.success) {
          if (result.payload.page) {
            setPage(result.payload.page);
          } else {
            setError(`Page with ID ${Id} not found`);
            toast.error(`Page with ID ${Id} not found`);
          }
        } else {
          setError("Error fetching page");
          toast.error("Error fetching page");
        }
      } catch (error) {
        console.error("Unexpected error:", error);
        setError("Failed to fetch page");
        toast.error("Failed to fetch page");
      } finally {
        setLoading(false);
      }
    };

    if (Id) {
      fetchPage();
    }
  }, [Id, sessionToken]);
  const handleUpdate = async (data: PageFormValues) => {
    try {
      const result = await pageApiRequest.updatePage( data, sessionToken );
      if (result.payload.success) {
        setPage(result.payload.page);
        toast.success("Thành Công");
      } else {
        toast.error("An error occurred during update. Please try again.");
        console.error("Error updating category:", result.payload.message);
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("An error occurred during update. Please try again.");
    }
  };

  if (loading) {
    return (
      <>
        <h1 className="text-2xl">Single Page</h1>
        <p>Loading...</p>
      </>
    );
  }

  if (error) {
    return (
      <>
        <h1 className="text-2xl">Single Page</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
          <p className="mt-2">
            <a href="/dashboard/secret/page" className="text-blue-600 hover:underline">
              ← Back to page list
            </a>
          </p>
        </div>
      </>
    );
  }

  return (
    <>
      <h1 className="text-2xl">Single Page</h1>
      {page && (
        <>
          <AddForm onSubmit={handleUpdate} page={page} />
          {page.revisions && <Revision post={page as any} />}
        </>
      )}
    </>
  );
}
