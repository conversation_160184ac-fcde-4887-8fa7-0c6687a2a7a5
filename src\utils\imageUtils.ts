import envConfig from "@/config";

/**
 * Get full image URL from relative path
 * @param imagePath - Relative path from backend (e.g., "uploads/single/thumb_image-123.jpg")
 * @returns Full URL or empty string if no path provided
 */
export function getImageUrl(imagePath: string | undefined | null): string {
  if (!imagePath) return "";
  
  // If already a full URL, return as is
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
    return imagePath;
  }
  
  // If starts with /, remove it to avoid double slashes
  const cleanPath = imagePath.startsWith("/") ? imagePath.slice(1) : imagePath;
  
  // Construct full URL
  const baseUrl = envConfig.NEXT_PUBLIC_API_ENDPOINT || "http://localhost:8000";
  return `${baseUrl}/${cleanPath}`;
}

/**
 * Get leader image URL with fallback
 * @param imagePath - Leader image path
 * @returns Full URL or placeholder
 */
export function getLeaderImageUrl(imagePath: string | undefined | null): string {
  const imageUrl = getImageUrl(imagePath);
  return imageUrl || "/imagenotavailable.png";
}
