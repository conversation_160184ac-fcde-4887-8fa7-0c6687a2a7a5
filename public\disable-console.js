// Disable console in production
(function() {
  'use strict';
  
  if (typeof window !== 'undefined' && window.location.hostname !== 'localhost') {
    // Override console methods
    const noop = function() {};
    const methods = ['log', 'debug', 'info', 'warn', 'error', 'assert', 'dir', 'dirxml', 'group', 'groupEnd', 'time', 'timeEnd', 'count', 'trace', 'profile', 'profileEnd'];
    
    for (let i = 0; i < methods.length; i++) {
      console[methods[i]] = noop;
    }
    
    // Note: Right-click is now allowed for better user experience

    // Disable F12, Ctrl+Shift+I, etc.
    document.addEventListener('keydown', function(e) {
      if (e.keyCode === 123 || // F12
          (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
          (e.ctrlKey && e.shiftKey && e.keyCode === 74) || // Ctrl+Shift+J
          (e.ctrl<PERSON>ey && e.keyCode === 85) || // Ctrl+U
          (e.ctrlKey && e.shiftKey && e.keyCode === 67)) { // Ctrl+Shift+C
        e.preventDefault();
        return false;
      }
    });
    
    // Clear any existing console content
    if (console.clear) {
      console.clear();
    }
  }
})();
