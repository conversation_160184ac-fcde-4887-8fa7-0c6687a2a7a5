const Leader = require("../models/leader");

// Get all departments (from leaders)
const getAllDepartments = async (req, res) => {
  try {
    // Get unique departments from leaders
    const departments = await Leader.distinct('department');
    
    // Filter out empty/null departments and create objects with id and name
    const departmentList = departments
      .filter(dept => dept && dept.trim())
      .map((dept, index) => ({
        id: index.toString(),
        name: dept
      }));

    res.status(200).json({
      success: true,
      data: departmentList,
      message: "<PERSON><PERSON>y danh sách phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in getAllDepartments:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách phòng ban"
    });
  }
};

// Create new department (just return success, will be created when leaders use it)
const createDepartment = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Check if department already exists
    const existingDepartment = await Leader.findOne({ department: name.trim() });
    if (existingDepartment) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }

    res.status(201).json({
      success: true,
      data: { id: Date.now().toString(), name: name.trim() },
      message: "Phòng ban được tạo thành công"
    });
  } catch (error) {
    console.error("Error in createDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo phòng ban"
    });
  }
};

// Update department name (update all leaders with old name to new name)
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Get unique departments to find the old name
    const departments = await Leader.distinct('department');
    const oldName = departments[parseInt(id)] || departments.find(d => d && d.includes(name));

    if (!oldName) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if new name already exists (and different from old name)
    if (oldName !== name.trim()) {
      const existingDepartment = await Leader.findOne({ department: name.trim() });
      if (existingDepartment) {
        return res.status(409).json({
          success: false,
          message: "Tên phòng ban đã tồn tại"
        });
      }
    }

    // Update all leaders with the old department name
    await Leader.updateMany(
      { department: oldName },
      { department: name.trim() }
    );

    res.status(200).json({
      success: true,
      data: { id, name: name.trim() },
      message: "Cập nhật phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in updateDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật phòng ban"
    });
  }
};

// Delete department (only if no leaders belong to it)
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    // Get unique departments to find the department name
    const departments = await Leader.distinct('department');
    const departmentName = departments[parseInt(id)] || departments.find(d => d);

    if (!departmentName) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if any leaders belong to this department
    const leadersCount = await Leader.countDocuments({ department: departmentName });
    if (leadersCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa! Còn ${leadersCount} lãnh đạo thuộc phòng ban này`
      });
    }

    res.status(200).json({
      success: true,
      message: "Xóa phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in deleteDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi xóa phòng ban"
    });
  }
};

module.exports = {
  getAllDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment
};
