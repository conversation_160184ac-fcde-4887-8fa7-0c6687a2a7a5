import NewsOne from "@/components/Block/NewsOne";
import NewsTwo from "@/components/Block/NewsTwo";
import NewsThree from "@/components/Block/NewsThree";
import NewsFour from "@/components/Block/NewsFour";
import NewsFive from "@/components/Block/NewsFive";
import NewsSix from "@/components/Block/NewsSix";
import NewsSeven from "@/components/Block/NewsSeven";
import blogApiRequest from "@/apiRequests/blog";
import Ads from "@/components/Ads";
import Image from 'next/image';
import backgroundImage from './background.jpg';

const componentMap: Record<number, React.ComponentType<any>> = {
  1: NewsOne,
  2: NewsTwo,
  3: NewsThree,
  4: NewsFour,
  5: NewsFive,
  6: NewsSix,
  7: NewsSeven,
};

export default async function Home() {
  const resPost = await blogApiRequest.fetchBlogHome();
  const allPostPerCat = resPost?.payload?.allPostPerCat ?? [];
  const newBlogs = resPost?.payload?.newBlogs ?? [];

  return (
    <div 
      style={{
        backgroundImage: `url(${backgroundImage.src})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundAttachment: "fixed"
      }}
      className="min-h-screen py-1 px-1 sm:py-1 sm:px-1"
    >
      <div className="container mx-auto">
        {/* Ad Banner */}
        <div className="mb-1 sm:mb-1">
          <Ads />
        </div>

        {/* Top News Section - Stacked on mobile, grid on larger screens */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-2 md:gap-4 mb-2 sm:mb-2">
          <div className="main-content col-span-1 md:col-span-3 bg-white dark:bg-gray-900 rounded-lg shadow-sm p-3 sm:p-4">
            <div className="w-full mb-4">
              <h2 className="text-lg sm:text-xl uppercase font-normal text-white py-2 px-3 w-full" style={{ backgroundColor: '#e4393c' }}>
                Tin Tức Nổi Bật
              </h2>
            </div>
            <NewsOne blogs={newBlogs.slice(0, 4)} />
          </div>
          
          {/* Second block - full width on mobile */}
          <div className="col-span-1 bg-gray-50 dark:bg-gray-800 rounded-lg shadow-sm p-3 sm:p-4">
            <NewsFour blogs={newBlogs.slice(4, 8)} />
          </div>
        </div>

        {/* Category Sections - Single column on mobile, 3 columns on larger screens */}
        <div className="grid auto-rows-auto grid-cols-1 md:grid-cols-3 gap-2 sm:gap-4">
          {allPostPerCat.map((cat, index) => {
            if (!cat?.posts || !cat.block) return null;

            const BlogComponent = componentMap[cat.block];
            if (!BlogComponent) return null;
            
            let blogsSlice = cat.posts;
            switch (cat.block) {
              case 1:
              case 2:
                blogsSlice = cat.posts.slice(0, 7);
                break;
              case 3:
                blogsSlice = cat.posts.slice(0, 8);
                break;
              case 4:
                blogsSlice = cat.posts.slice(0, 4 + index);
                break;
              case 5:
                blogsSlice = cat.posts.slice(0, 3);
                break;
              case 6:
                blogsSlice = cat.posts.slice(0, 4);
                break;
              case 7:
                blogsSlice = cat.posts; // Full array
                break;
              default:
                blogsSlice = cat.posts;
            }

            return (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-lg shadow-sm p-3 sm:p-2">
                <BlogComponent
                  blogs={blogsSlice}
                  category={cat}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}