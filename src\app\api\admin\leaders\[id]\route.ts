import { NextRequest, NextResponse } from "next/server";
import { UpdateLeaderBodySchema } from "@/schemaValidations/leader.schema";

// GET - Get specific leader (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/leaders/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      cache: 'no-store'
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi lấy thông tin lãnh đạo" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Lấy thông tin lãnh đạo thành công"
    });

  } catch (error) {
    console.error("Error in admin leader GET:", error);
    return NextResponse.json(
      { success: false, message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}

// PUT - Update leader (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;
    const body = await request.json();
    
    // Validate request body
    const validatedData = UpdateLeaderBodySchema.parse(body);

    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/leaders/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      body: JSON.stringify(validatedData),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi cập nhật thông tin lãnh đạo" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Cập nhật thông tin lãnh đạo thành công"
    });

  } catch (error) {
    console.error("Error in admin leader PUT:", error);
    
    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json(
        { message: "Dữ liệu không hợp lệ", errors: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}

// DELETE - Delete leader (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/leaders/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi xóa lãnh đạo" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      message: data.message || "Xóa lãnh đạo thành công"
    });

  } catch (error) {
    console.error("Error in admin leader DELETE:", error);
    return NextResponse.json(
      { success: false, message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}
