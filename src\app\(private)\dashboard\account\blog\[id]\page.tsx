"use client";
import { useEffect, useState, use } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import AddForm from "@/components/Form/AddBlog";
import { BlogRes, BlogUserCreate } from "@/schemaValidations/blog.schema";
import blogApiRequest from "@/apiRequests/blog";
import { toast } from "react-toastify";
import { z } from "zod";
import { refreshAllVideos } from "@/utils/videoUtils";

type BlogFormValues = z.infer<typeof BlogUserCreate>[0];
export default function EditBlog({ params }: { params: Promise<{ id: any }> }) {
  const [blog, setBlog] = useState<BlogFormValues | null>(null);
  const [sessionToken, setSessionToken] = useState<string>("");
  const resolvedParams = use(params);
  const Id = resolvedParams.id;
  const router = useRouter();

  useEffect(() => {
    // Get session token on client side only
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  useEffect(() => {
    if (!sessionToken) return; // Wait for session token to be loaded

    const fetchBlog = async () => {
      try {
        const result = await blogApiRequest.userFetchBlogById(Id, sessionToken);
        if (result.payload.success) {
          setBlog(result.payload.post);

          // Force refresh videos after blog data is loaded
          setTimeout(() => {
            refreshAllVideos();
          }, 1000);
        } else {
          toast.error("Error fetching blog.");
          console.error("Error fetching category:", result.message);
        }
      } catch (error) {
        console.error("Unexpected error:", error);
      }
    };

    if (Id) {
      fetchBlog();
    }
  }, [Id, sessionToken]);

  // Force refresh videos on page load
  useEffect(() => {
    const intervals = [500, 1000, 2000, 3000];
    const timeouts = intervals.map(delay =>
      setTimeout(() => {
        refreshAllVideos();
      }, delay)
    );

    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  if (!blog) {
    return <p>Loading...</p>; // Ensure `blog` is loaded before rendering
  }

  const handleUpdate = async (data: BlogFormValues) => {
    try {
      const result = await blogApiRequest.userUpdateBlog(data, sessionToken);
      if (result.payload.success) {
        setBlog(result.payload.post);
        toast.success("Thành Công");
      } else {
        toast.error("An error occurred during update. Please try again.");
        console.error("Error creating Blog:", result.payload.message);
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("An error occurred during update. Please try again.");
    }
  };

  return (
    <>
      <h1 className="text-2xl"> Blog</h1>
      <AddForm onSubmit={handleUpdate} blog={blog} />
    </>
  );
}
