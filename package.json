{"name": "cdn", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "hello", "start": "node server.js", "dev": "nodemon server.js", "sync-files": "node sync-existing-files.js"}, "author": "Mr.3T", "license": "ISC", "dependencies": {"@casl/ability": "^5.4.3", "@casl/mongoose": "^6.0.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.11.2", "helmet": "^8.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "moment": "^2.29.1", "mongoose": "^6.13.8", "morgan": "^1.10.0", "multer": "^2.0.1", "nanoid": "^3.3.1", "nodemailer": "^6.7.2", "passport": "^0.7.0", "passport-jwt": "^4.0.0", "qrcode": "^1.5.4", "request-ip": "^3.3.0", "sharp": "^0.34.2", "speakeasy": "^2.0.0", "validator": "^13.15.15", "xss": "^1.0.15"}, "devDependencies": {"eslint": "^8.9.0", "nodemon": "^2.0.15"}}