const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// Security configurations
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB (reduced from 340MB)
const MAX_FILES = 10; // Reduced from 100 files

// Secure allowed extensions with MIME type validation
const ALLOWED_FILE_TYPES = {
  // Documents
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/vnd.ms-excel': ['.xls'],
  'text/plain': ['.txt'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
  // Archives
  'application/x-rar-compressed': ['.rar'],
  'application/zip': ['.zip'],
  'application/x-zip-compressed': ['.zip']
};

// Get all allowed extensions
const getAllowedExtensions = () => {
  return Object.values(ALLOWED_FILE_TYPES).flat();
};

// Validate file type against both extension and MIME type
const validateFileType = (file) => {
  const fileExt = path.extname(file.originalname).toLowerCase();
  const mimeType = file.mimetype.toLowerCase();
  
  // Check if MIME type is allowed
  if (!ALLOWED_FILE_TYPES[mimeType]) {
    return false;
  }
  
  // Check if extension matches the MIME type
  return ALLOWED_FILE_TYPES[mimeType].includes(fileExt);
};

// Generate secure filename
const generateSecureFilename = (originalname) => {
  const fileExt = path.extname(originalname).toLowerCase();
  const randomName = crypto.randomBytes(16).toString('hex');
  const timestamp = Date.now();
  return `${timestamp}-${randomName}${fileExt}`;
};

// Sanitize filename to prevent path traversal
const sanitizeFilename = (filename) => {
  return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
};

// Create secure storage configuration
const createSecureStorage = (uploadDir) => {
  // Ensure upload directory exists
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true, mode: 0o755 });
  }
  
  return multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
      try {
        if (!validateFileType(file)) {
          return cb(new Error('File type not allowed'), null);
        }
        
        const secureFilename = generateSecureFilename(file.originalname);
        cb(null, secureFilename);
      } catch (error) {
        cb(error, null);
      }
    }
  });
};

// Secure file filter
const secureFileFilter = (req, file, cb) => {
  try {
    // Validate file type
    if (!validateFileType(file)) {
      return cb(new Error('File type not allowed. Only documents and archives are permitted.'), false);
    }
    
    // Additional security checks
    const filename = file.originalname.toLowerCase();
    
    // Block potential executable files
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.js', '.vbs', '.ps1'];
    if (dangerousExtensions.some(ext => filename.endsWith(ext))) {
      return cb(new Error('Executable files are not allowed'), false);
    }
    
    // Block files with multiple extensions
    const extCount = (filename.match(/\./g) || []).length;
    if (extCount > 1) {
      return cb(new Error('Files with multiple extensions are not allowed'), false);
    }
    
    cb(null, true);
  } catch (error) {
    cb(error, false);
  }
};

// Create secure multer configuration
const createSecureUpload = (uploadDir, options = {}) => {
  const storage = createSecureStorage(uploadDir);
  
  return multer({
    storage: storage,
    limits: {
      fileSize: options.maxFileSize || MAX_FILE_SIZE,
      files: options.maxFiles || MAX_FILES,
      fieldNameSize: 100,
      fieldSize: 1024 * 1024, // 1MB
      fields: 10
    },
    fileFilter: secureFileFilter
  });
};

module.exports = {
  createSecureUpload,
  validateFileType,
  generateSecureFilename,
  sanitizeFilename,
  getAllowedExtensions,
  MAX_FILE_SIZE,
  MAX_FILES,
  ALLOWED_FILE_TYPES
};
